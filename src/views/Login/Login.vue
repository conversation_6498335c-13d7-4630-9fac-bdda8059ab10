<template>
    <div class="modern-login">
        <!-- 背景装饰 -->
        <div class="bg-decoration">
            <div class="bg-circle circle-1"></div>
            <div class="bg-circle circle-2"></div>
            <div class="bg-circle circle-3"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="login-container">
            <!-- 左侧品牌区域 -->
            <div class="brand-section">
                <div class="brand-content">
                    <div class="logo-area">
                        <div class="logo-icon">
                            <i class="el-icon-office-building"></i>
                        </div>
                        <h1 class="brand-title">智慧建筑运行管理平台</h1>
                    </div>
                    <p class="brand-subtitle">现代化智能建筑管理解决方案</p>
                    <div class="feature-list">
                        <div class="feature-item">
                            <i class="el-icon-check"></i>
                            <span>实时监控建筑设备状态</span>
                        </div>
                        <div class="feature-item">
                            <i class="el-icon-check"></i>
                            <span>智能化能耗管理</span>
                        </div>
                        <div class="feature-item">
                            <i class="el-icon-check"></i>
                            <span>数据可视化分析</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧登录表单区域 -->
            <div class="form-section">
                <div class="form-container">
                    <!-- 登录表单 -->
                    <div class="login-form-wrapper" v-if="!register">
                        <div class="form-header">
                            <h2>欢迎回来</h2>
                            <p>请登录您的账户</p>
                        </div>

                        <el-form class="login-form" :model="form" ref="ruleForm" :rules="rules">
                            <el-form-item prop="username">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-user"></i>
                                    <el-input
                                        type="text"
                                        v-model="form.username"
                                        placeholder="请输入用户名"
                                        size="large">
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-form-item prop="password">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-lock"></i>
                                    <el-input
                                        type="password"
                                        v-model="form.password"
                                        placeholder="请输入密码"
                                        size="large"
                                        show-password>
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-form-item prop="code" v-if="form.username.toLowerCase() === 'demo'">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-key"></i>
                                    <el-input
                                        type="text"
                                        v-model="form.code"
                                        placeholder="请输入邀请码"
                                        size="large">
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-button
                                :loading="isLogin"
                                @click="handleSubmit"
                                type="primary"
                                class="login-btn"
                                size="large">
                                <span v-if="!isLogin">立即登录</span>
                                <span v-else>登录中...</span>
                            </el-button>
                        </el-form>

                        <div class="form-footer">
                            <a href="#" class="forgot-link">忘记密码？</a>
                            <span class="divider">|</span>
                            <a href="#" @click="clickRegister" class="register-link">注册账户</a>
                        </div>
                    </div>

                    <!-- 注册表单 -->
                    <div class="register-form-wrapper" v-if="register">
                        <div class="form-header">
                            <button class="back-btn" @click="back">
                                <i class="el-icon-arrow-left"></i>
                            </button>
                            <h2>创建账户</h2>
                            <p>请填写注册信息</p>
                        </div>

                        <el-form class="register-form" :model="userForm" ref="registerForm" :rules="regRules">
                            <el-form-item prop="name">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-user"></i>
                                    <el-input
                                        type="text"
                                        v-model="userForm.name"
                                        placeholder="请输入用户名"
                                        size="large">
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-form-item prop="userName">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-user-solid"></i>
                                    <el-input
                                        type="text"
                                        v-model="userForm.userName"
                                        placeholder="请输入登录名"
                                        size="large">
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-form-item prop="phone">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-phone"></i>
                                    <el-input
                                        v-model.number="userForm.phone"
                                        placeholder="请输入手机号"
                                        size="large">
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-form-item prop="email">
                                <div class="input-wrapper">
                                    <i class="input-icon el-icon-message"></i>
                                    <el-input
                                        v-model="userForm.email"
                                        placeholder="请输入邮箱"
                                        size="large">
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-button
                                :loading="isLogin"
                                @click="handleRegister"
                                type="primary"
                                class="register-btn"
                                size="large">
                                <span v-if="!isLogin">立即注册</span>
                                <span v-else>注册中...</span>
                            </el-button>
                        </el-form>
                    </div>
                </div>
            </div>
        </div>

        <Verify @success="login" @close="close" :captchaType="'blockPuzzle'"
            :imgSize="{ width: '400px', height: '200px' }" ref="verify">
        </Verify>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { useRouter } from 'vue-router';
import Verify from '@/components/verifition/Verify.vue';
import {
    encode
} from 'js-base64';
import {
    getCookie,setCookie
} from '@/utils/cookie';
import {
    ElMessage
} from 'element-plus';
import { useAppStore } from '@/stores/app';
import useSocket from '@/hooks/socket';

const api = inject('$api')
const store = useAppStore();
const router = useRouter();
const registerForm = ref();
const register = ref(false);
const verify = ref();
const ruleForm = ref();
const form = ref({
    username: '',
    password: '',
    code: '',
    params: '',
    autoLogin: true
})
const userForm = ref({
    userName: '',
    name: '',
    phone: '',
    email: ''
})


const isLogin = ref(false);

const rules = ref({

    username: [{
        required: true,
        message: '请输入账号',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    code: [{
        required: true,
        message: '请输入邀请码',
        trigger: 'blur'
    }],


})

const regRules = ref({
    userName: [{
        required: true,
        message: '请输入登录名',
        trigger: 'blur'
    }],
    name: [{
        required: true,
        message: '请输入用户名',
        trigger: 'blur'
    }],
    password: [{
        required: true,
        message: '请输入密码',
        trigger: 'blur'
    }],
    email: [{
        required: true,
        message: '请输入邮箱',
        trigger: 'blur'
    }, {
        validator: (_, value, callback) => {
            const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error('请输入正确的邮箱'));
            }
        },
        trigger: 'blur'
    }],
    phone: [{
        required: true,
        message: '请输入手机号',
        trigger: 'blur'
    }, {
        validator: (_, value, callback) => {
            const reg = /^1(3\d|4[5-9]|5[0-35-9]|6[2567]|7[0-8]|8\d|9[0-35-9])\d{8}$/;
            if (reg.test(value)) {
                callback();
            } else {
                callback(new Error("请输入正确的手机号码"));
            }
        },
        trigger: 'blur'
    }],
})



const { socketEmit } = useSocket()

const handleSubmit = () => {
    ruleForm.value.validate((valid) => {
        if (valid) {
            //login();
            isLogin.value = true;
            verify.value.show();
        }
    });
};
const clickRegister = () => {
    register.value = true
}
// 注册
const handleRegister = () => {
    registerForm.value.validate((valid) => {
        if (valid) {
            api.register(userForm.value).then(res => {
                if (res.success) {
                    register.value = false;
                    ElMessage({
                        message: '注册成功',
                        type: 'success'
                    });
                    router.push({
                        path: '/login'
                    })
                }
            })
        } else {
            return false
        }
    })
}
// 登录
const login = async () => {
    try {


        const { data } = await api.login({
            username: encode(form.value.username),
            password: encode(form.value.password),
            code: form.value.code,
            params: { captchaVerification: null }
        });

        store.SET_TOKEN(data.access_token);
        setCookie("gh_token", data.access_token);
        setCookie("gh_id", data.userId);
        setCookie("gh_name", data.name);
        setCookie("_refreshToken", data.refresh_token);

        const res = await api.getDefaultProject()
        setCookie("gh_projectId", res.data);
        store.SET_PROJECT_ID(res.data)

        router.push({
            path: '/'
        })
        socketEmit('SetProject', getCookie('gh_projectId'));
        socketEmit('SetUserId', getCookie('gh_id'));
    } catch (e) {
        isLogin.value = false;
    }
};
const back = () => {
    register.value = false
}
const close = () => {
    isLogin.value = false;
}

</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.modern-login {
    position: relative;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;

    // 背景装饰
    .bg-decoration {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1;

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            animation: float 6s ease-in-out infinite;

            &.circle-1 {
                width: 200px;
                height: 200px;
                top: 10%;
                left: 10%;
                animation-delay: 0s;
            }

            &.circle-2 {
                width: 150px;
                height: 150px;
                top: 60%;
                right: 15%;
                animation-delay: 2s;
            }

            &.circle-3 {
                width: 100px;
                height: 100px;
                bottom: 20%;
                left: 20%;
                animation-delay: 4s;
            }
        }
    }

    // 主容器
    .login-container {
        position: relative;
        z-index: 2;
        width: 100%;
        max-width: 1200px;
        height: 600px;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        display: flex;
        overflow: hidden;

        // 左侧品牌区域
        .brand-section {
            flex: 1;
            background: linear-gradient(135deg, $themeColor 0%, #1196fc 100%);
            padding: 60px 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
                opacity: 0.3;
            }

            .brand-content {
                position: relative;
                z-index: 1;
                text-align: center;
                color: white;

                .logo-area {
                    margin-bottom: 30px;

                    .logo-icon {
                        width: 80px;
                        height: 80px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 20px;
                        backdrop-filter: blur(10px);
                        border: 1px solid rgba(255, 255, 255, 0.3);

                        i {
                            font-size: 36px;
                            color: white;
                        }
                    }

                    .brand-title {
                        font-size: 32px;
                        font-weight: 700;
                        margin: 0;
                        line-height: 1.2;
                        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                    }
                }

                .brand-subtitle {
                    font-size: 18px;
                    opacity: 0.9;
                    margin-bottom: 40px;
                    font-weight: 300;
                }

                .feature-list {
                    text-align: left;
                    max-width: 300px;
                    margin: 0 auto;

                    .feature-item {
                        display: flex;
                        align-items: center;
                        margin-bottom: 15px;
                        font-size: 16px;
                        opacity: 0.9;

                        i {
                            width: 20px;
                            height: 20px;
                            background: rgba(255, 255, 255, 0.2);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin-right: 12px;
                            font-size: 12px;
                        }
                    }
                }
            }
        }

        // 右侧表单区域
        .form-section {
            flex: 1;
            padding: 60px 50px;
            display: flex;
            align-items: center;
            justify-content: center;

            .form-container {
                width: 100%;
                max-width: 400px;

                .form-header {
                    text-align: center;
                    margin-bottom: 40px;
                    position: relative;

                    .back-btn {
                        position: absolute;
                        left: 0;
                        top: 0;
                        width: 40px;
                        height: 40px;
                        border: none;
                        background: #f5f5f5;
                        border-radius: 10px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        cursor: pointer;
                        transition: all 0.3s ease;

                        &:hover {
                            background: #e0e0e0;
                            transform: translateX(-2px);
                        }

                        i {
                            font-size: 18px;
                            color: #666;
                        }
                    }

                    h2 {
                        font-size: 28px;
                        font-weight: 700;
                        color: #333;
                        margin: 0 0 8px 0;
                    }

                    p {
                        color: #666;
                        font-size: 16px;
                        margin: 0;
                    }
                    // 表单样式
                    .login-form, .register-form {
                        .input-wrapper {
                            position: relative;
                            margin-bottom: 20px;

                            .input-icon {
                                position: absolute;
                                left: 16px;
                                top: 50%;
                                transform: translateY(-50%);
                                color: #999;
                                font-size: 18px;
                                z-index: 2;
                                transition: color 0.3s ease;
                            }

                            :deep(.el-input) {
                                .el-input__wrapper {
                                    background: #f8f9fa;
                                    border: 2px solid #e9ecef;
                                    border-radius: 12px;
                                    box-shadow: none;
                                    transition: all 0.3s ease;
                                    padding-left: 50px;

                                    &:hover {
                                        border-color: #dee2e6;
                                        background: #f1f3f4;
                                    }

                                    &.is-focus {
                                        border-color: $themeColor;
                                        background: #fff;
                                        box-shadow: 0 0 0 3px rgba(61, 233, 250, 0.1);
                                    }
                                }

                                .el-input__inner {
                                    color: #333;
                                    font-size: 16px;
                                    padding-left: 50px;

                                    &::placeholder {
                                        color: #adb5bd;
                                    }
                                }
                            }

                            &:focus-within .input-icon {
                                color: $themeColor;
                            }
                        }

                        :deep(.el-form-item) {
                            margin-bottom: 0;
                        }
                    }

                    // 登录按钮
                    .login-btn, .register-btn {
                        width: 100%;
                        height: 50px;
                        background: linear-gradient(135deg, $themeColor 0%, #1196fc 100%);
                        border: none;
                        border-radius: 12px;
                        color: white;
                        font-size: 16px;
                        font-weight: 600;
                        margin-top: 30px;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 15px rgba(61, 233, 250, 0.3);

                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 6px 20px rgba(61, 233, 250, 0.4);
                        }

                        &:active {
                            transform: translateY(0);
                        }

                        &.is-loading {
                            opacity: 0.8;
                            cursor: not-allowed;
                        }
                    }

                    // 表单底部
                    .form-footer {
                        text-align: center;
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #e9ecef;

                        .forgot-link, .register-link {
                            color: #666;
                            text-decoration: none;
                            font-size: 14px;
                            transition: color 0.3s ease;

                            &:hover {
                                color: $themeColor;
                            }
                        }

                        .divider {
                            margin: 0 15px;
                            color: #dee2e6;
                        }
                    }
                }
            }
        }
    }

    // 动画
    @keyframes float {
        0%, 100% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-20px);
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .login-container {
            flex-direction: column;
            height: auto;
            margin: 20px;
            max-width: none;

            .brand-section {
                padding: 40px 30px;

                .brand-content {
                    .logo-area .brand-title {
                        font-size: 24px;
                    }

                    .brand-subtitle {
                        font-size: 16px;
                    }
                }
            }

            .form-section {
                padding: 40px 30px;
            }
        }
    }
}
</style>
